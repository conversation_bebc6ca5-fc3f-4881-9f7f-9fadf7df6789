{"global": {"page": ", Page {page}"}, "extractor": {"client": {"download": "Download", "downloadVideo": "Download Video", "downloadThumbnail": "Download Thumbnail", "downloadImage": "Download Image", "downloadAudio": "Download Audio", "moreQualityOptions": "More Quality Options", "moreAudioOptions": "More Audio Options", "noInput": "Please paste the link in the input box above", "wrongLink": "Wrong link format. Please correct link and try again", "requestTimeout": "Request timeout. please try again", "networkError": "Network error, Please check the network connection and try again", "serverError": "Something went wrong, please try again", "placeholder": "Paste the link here", "resolutionDownloadTip": "Click on your desired resolution to download. If download fails, try alternative download links", "alternativeLink": "Alternative Link", "downloadTip": "If the file does not download directly after clicking the download button, please try to ", "desktopTip": "right-click the download button and choose \"Save Link As...\".", "mobileTip": "long-press the download button.", "iosTip": "read the FAQ below.", "sponsors": "Sponsors", "recommendedProProducts": "These professional products are recommended for you."}, "refreshPage": "Please force refresh the current page and try again", "illegalRequest": "Illegal request. Please refresh the page and try again", "frequencyLimitExceeded": "Your operation is too frequent, please try again later", "operationFailure": "Operation failure. Please correct link and try again", "unsupportedSiteError": "This website is not supported yet, please submit feedback", "privateUrlError": "Downloading private link content is not currently supported", "notSupportLiveError": "Downloading live videos is not currently supported", "notSupportBatchError": "Please paste individual post link", "deletedContentError": "Invalid link, content may have been deleted", "reviewingContentError": "Invalid link, the content may be under review or has been deleted", "vipUrlError": "Downloading paid content is currently not supported", "notYetPremieredError": "The content has not yet premiered", "retryableError": "Operation failed, please try again later", "unsupportedM3U8": "The browser cannot directly download M3U8-format videos"}, "header": {"downloader": "Downloader", "converter": "Converter", "more": "More", "joinTelegramGroup": "Join Telegram Group", "joinDiscordServer": "Join Discord Community", "downloadApp": "Download Powerful Desktop App"}, "footer": {"tools": "Tools", "articles": "Articles", "about": "About", "languages": "Languages", "contact": "Contact Us", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "contactEmail": "Contact us for any questions or feedback at {email}.", "telegramGroup": "Telegram Group", "discordServer": "Discord Server", "helpCenter": "Help Center"}, "app": {"title": "SnapAny Desktop App", "subTitle": "Download Videos, Music, and Images from 10,000+ Sites in One Click", "subTitle2": "Fast, Free, and Powerful", "featureTitle": "Your Ultimate Download Solution", "feature1": "One-Click Download for Videos, Music, and Images", "feature1Desc": "Easily download videos, music, and images from over 10,000 sites with a single click. Just paste the link, and SnapAny will extract the content effortlessly—fast, simple, and hassle-free.", "feature2": "Lightning-Fast Downloads – Up to 10x Faster", "feature2Desc": "Powered by advanced acceleration technology, SnapAny ensures ultra-fast downloads. Large files? No problem! Enjoy speeds up to 10x faster, saving you valuable time.", "feature3": "Supports Ultra HD 8K Resolution", "feature3Desc": "Enjoy downloading videos in stunning 4K and 8K resolution. Experience crystal-clear visuals and unparalleled quality for the best viewing experience.", "feature4": "Smart Subtitle Download", "feature4Desc": "Automatically detect and extract subtitles in multiple languages. Download them as standalone files or embed them directly into your videos for a seamless viewing experience.", "feature5": "Batch Download Playlists & Channel Videos", "feature5Desc": "Download entire playlists or channel videos in one go! No need to grab videos one by one—SnapAny makes bulk downloading easy and efficient. Perfect for binge-watchers and content collectors.", "feature6": "Download Specific Audio Tracks", "feature6Desc": "Choose the audio track you want when downloading videos. Whether it's different languages, high-quality audio, or specific soundtracks, SnapAny gives you full control over your downloads.", "supportedSiteTitle": "10,000+ Supported Sites", "supportedSiteSubTitle": "Download videos, music, and images from all your favorite platforms with ease.", "supportedSiteSubTitle2": "SnapAny supports 10,000+ popular sites, ensuring seamless and reliable downloads.", "fullListOfSupportedSites": "See Full List of Supported Sites", "faq": "Frequently Asked Questions", "faqDesc": "Need more help? Join our community on <discord>Discord</discord> or <telegram>Telegram</telegram> to ask questions, share feedback, and connect with other users!"}, "appDownloadButtons": {"downloadForWindows": "Download for Windows", "windowsDesc": "For Win 10 (64 bit) / Win 11", "downloadForMac": "Download for Mac", "macDesc": "For macOS 10.15 or later", "macChipModalTitle": "Please choose your chip's version", "macChipModalDescription": "Click <apple>Apple</apple> and select 'About This Mac' to view the chip type", "downloadForAppleChip": "Download for Apple Chip", "downloadForIntel": "Download for Intel"}, "downloader": {"title": "SnapAny Video Download Extension", "subtitle": "Install the extension to quickly download videos from any site. Easy to use, safe, and completely free—forever.", "placeholder": "Downloader interface will be available here", "placeholderDesc": "This is a placeholder for the advanced downloader component that will be migrated from the desktop application.", "featuresTitle": "Advanced Features", "feature1Title": "Detects Videos Automatically", "feature1Desc": "No need to copy links — SnapAny recognizes playable videos or audio on the page for you.", "feature2Title": "Just One Click to Save", "feature2Desc": "After detection, you can start downloading with a single click. No complicated steps.", "feature3Title": "Fast and Reliable Downloads", "feature3Desc": "Enjoy smooth and stable downloads, even with large files or long videos.", "howToUseTitle": "Save any video or audio in 3 quick steps", "step1Title": "Open the Media Page", "step1Desc": "Go to the page where your video or audio is playing. SnapAny will auto-detect all available media.", "step2Title": "Click the SnapAny Icon", "step2Desc": "Click the SnapAny icon in your browser toolbar to open the detected file list.", "step3Title": "Download Your File", "step3Desc": "Pick your file and hit the download icon. A new tab will open to show download progress — keep it open until complete."}, "downloadComponents": {"stopRecording": "Stop Recording", "retry": "Retry", "save": "Save", "waitingDownload": "Waiting for download", "unknownFile": "Unknown file", "downloadError": "Download error occurred", "fileSizeUnits": {"B": "B", "KB": "KB", "MB": "MB", "GB": "GB"}}}